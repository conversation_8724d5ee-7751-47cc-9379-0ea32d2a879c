import React, { useState, useEffect } from 'react'
import { use<PERSON><PERSON><PERSON>, Link, useNavigate } from 'react-router-dom'
import { useTranslation } from 'react-i18next'
import {
  ArrowLeftIcon,
  LightBulbIcon,
  ChartBarIcon,
  ArrowTrendingUpIcon,
  ArrowTrendingDownIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
  ClockIcon,
  CurrencyDollarIcon,
  GlobeAmericasIcon,
  CalendarIcon,
  ShoppingCartIcon
} from '@heroicons/react/24/outline'
import AIService from '../../services/AIService'

const RecommendationDetails = () => {
  const { id } = useParams()
  const navigate = useNavigate()
  const { t } = useTranslation()

  const [recommendation, setRecommendation] = useState(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState(null)
  const [relatedRecommendations, setRelatedRecommendations] = useState([])

  useEffect(() => {
    fetchRecommendationDetails()
  }, [id])

  const fetchRecommendationDetails = async () => {
    try {
      setIsLoading(true)
      setError(null)

      // For now, we'll simulate fetching a specific recommendation
      // In a real implementation, you'd have an API endpoint for individual recommendations
      const response = await AIService.getRecommendations()

      if (response && response.data && response.data.recommendations) {
        // Find the recommendation with the matching ID
        const foundRecommendation = response.data.recommendations.find(
          rec => rec.id === parseInt(id) || rec.id === id
        )

        if (foundRecommendation) {
          setRecommendation(foundRecommendation)

          // Get related recommendations (exclude current one)
          const related = response.data.recommendations
            .filter(rec => rec.id !== foundRecommendation.id)
            .slice(0, 3)
          setRelatedRecommendations(related)
        } else {
          setError(t('ai.recommendationNotFound'))
        }
      } else {
        setError(t('ai.errorFetchingRecommendation'))
      }
    } catch (error) {
      console.error('Error fetching recommendation details:', error)
      setError(t('ai.errorFetchingRecommendation'))
    } finally {
      setIsLoading(false)
    }
  }

  const getRecommendationIcon = (action) => {
    switch (action?.toLowerCase()) {
      case 'buy':
        return <ArrowTrendingUpIcon className="h-6 w-6 text-green-600" />
      case 'sell':
        return <ArrowTrendingDownIcon className="h-6 w-6 text-red-600" />
      case 'hold':
        return <ClockIcon className="h-6 w-6 text-yellow-600" />
      default:
        return <LightBulbIcon className="h-6 w-6 text-blue-600" />
    }
  }

  const getRecommendationBadge = (action) => {
    const baseClasses = "inline-flex items-center px-3 py-1 rounded-full text-sm font-medium"

    switch (action?.toLowerCase()) {
      case 'buy':
        return `${baseClasses} bg-green-100 text-green-800`
      case 'sell':
        return `${baseClasses} bg-red-100 text-red-800`
      case 'hold':
        return `${baseClasses} bg-yellow-100 text-yellow-800`
      default:
        return `${baseClasses} bg-blue-100 text-blue-800`
    }
  }

  const getConfidenceColor = (confidence) => {
    if (confidence >= 80) return 'text-green-600'
    if (confidence >= 60) return 'text-yellow-600'
    return 'text-red-600'
  }

  if (isLoading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-green-500"></div>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="bg-red-50 p-4 rounded-md">
          <div className="flex">
            <div className="flex-shrink-0">
              <ExclamationTriangleIcon className="h-5 w-5 text-red-400" />
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-red-800">{error}</h3>
              <div className="mt-2">
                <Link
                  to="/ai/recommendations"
                  className="text-sm font-medium text-red-800 hover:text-red-700"
                >
                  {t('common.goBack')}
                </Link>
              </div>
            </div>
          </div>
        </div>
      </div>
    )
  }

  if (!recommendation) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center py-12 bg-gray-50 rounded-lg">
          <p className="text-gray-500 mb-4">{t('ai.recommendationNotFound')}</p>
          <Link
            to="/ai/recommendations"
            className="inline-flex items-center px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors"
          >
            <ArrowLeftIcon className="h-5 w-5 mr-2" />
            {t('common.back')}
          </Link>
        </div>
      </div>
    )
  }

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Back Link */}
      <div className="mb-6">
        <Link
          to="/ai/recommendations"
          className="inline-flex items-center text-green-600 hover:text-green-700"
        >
          <ArrowLeftIcon className="h-4 w-4 mr-1" />
          {t('common.back')}
        </Link>
      </div>

      {/* Header */}
      <div className="bg-white rounded-lg shadow-md overflow-hidden mb-8">
        <div className="p-6 border-b border-gray-200">
          <div className="flex flex-col md:flex-row md:items-center md:justify-between">
            <div className="flex items-center mb-4 md:mb-0">
              {getRecommendationIcon(recommendation.action)}
              <div className="ml-3">
                <h1 className="text-2xl font-bold text-gray-900">
                  {recommendation.product_type || recommendation.product}
                </h1>
                <p className="text-gray-600">
                  {recommendation.region || t('ai.globalMarket')}
                </p>
              </div>
            </div>
            <div className="flex items-center space-x-4">
              <span className={getRecommendationBadge(recommendation.action)}>
                {recommendation.action || t('ai.recommendation')}
              </span>
              {recommendation.confidence && (
                <div className="text-right">
                  <p className="text-sm text-gray-500">{t('ai.confidence')}</p>
                  <p className={`text-lg font-semibold ${getConfidenceColor(recommendation.confidence)}`}>
                    {recommendation.confidence}%
                  </p>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Key Metrics */}
        <div className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            {recommendation.current_price && (
              <div className="text-center">
                <CurrencyDollarIcon className="h-8 w-8 text-green-600 mx-auto mb-2" />
                <p className="text-sm text-gray-500">{t('ai.currentPrice')}</p>
                <p className="text-xl font-bold text-gray-900">
                  ${recommendation.current_price}
                </p>
              </div>
            )}

            {recommendation.predicted_price && (
              <div className="text-center">
                <ArrowTrendingUpIcon className="h-8 w-8 text-blue-600 mx-auto mb-2" />
                <p className="text-sm text-gray-500">{t('ai.predictedPrice')}</p>
                <p className="text-xl font-bold text-gray-900">
                  ${recommendation.predicted_price}
                </p>
              </div>
            )}

            {recommendation.price_change && (
              <div className="text-center">
                <ChartBarIcon className="h-8 w-8 text-purple-600 mx-auto mb-2" />
                <p className="text-sm text-gray-500">{t('ai.expectedChange')}</p>
                <p className={`text-xl font-bold ${
                  recommendation.price_change > 0 ? 'text-green-600' : 'text-red-600'
                }`}>
                  {recommendation.price_change > 0 ? '+' : ''}{recommendation.price_change}%
                </p>
              </div>
            )}

            {recommendation.timeframe && (
              <div className="text-center">
                <CalendarIcon className="h-8 w-8 text-orange-600 mx-auto mb-2" />
                <p className="text-sm text-gray-500">{t('ai.timeframe')}</p>
                <p className="text-xl font-bold text-gray-900">
                  {recommendation.timeframe}
                </p>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Recommendation Details */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Main Content */}
        <div className="lg:col-span-2 space-y-6">
          {/* Description */}
          {recommendation.description && (
            <div className="bg-white rounded-lg shadow-md p-6">
              <h2 className="text-lg font-semibold text-gray-900 mb-4">
                {t('ai.recommendationDetails')}
              </h2>
              <p className="text-gray-700 leading-relaxed">
                {recommendation.description}
              </p>
            </div>
          )}

          {/* Factors Considered */}
          {recommendation.factors && recommendation.factors.length > 0 && (
            <div className="bg-white rounded-lg shadow-md p-6">
              <h2 className="text-lg font-semibold text-gray-900 mb-4">
                {t('ai.factorsConsidered')}
              </h2>
              <ul className="space-y-2">
                {recommendation.factors.map((factor, index) => (
                  <li key={index} className="flex items-start">
                    <CheckCircleIcon className="h-5 w-5 text-green-500 mt-0.5 mr-2 flex-shrink-0" />
                    <span className="text-gray-700">{factor}</span>
                  </li>
                ))}
              </ul>
            </div>
          )}

          {/* Risk Assessment */}
          {recommendation.risk_level && (
            <div className="bg-white rounded-lg shadow-md p-6">
              <h2 className="text-lg font-semibold text-gray-900 mb-4">
                {t('ai.riskAssessment')}
              </h2>
              <div className="flex items-center mb-3">
                <span className="text-sm font-medium text-gray-500 mr-2">
                  {t('ai.riskLevel')}:
                </span>
                <span className={`px-3 py-1 rounded-full text-sm font-medium ${
                  recommendation.risk_level === 'low' ? 'bg-green-100 text-green-800' :
                  recommendation.risk_level === 'medium' ? 'bg-yellow-100 text-yellow-800' :
                  'bg-red-100 text-red-800'
                }`}>
                  {recommendation.risk_level}
                </span>
              </div>
              {recommendation.risk_factors && (
                <ul className="text-sm text-gray-600 space-y-1">
                  {recommendation.risk_factors.map((risk, index) => (
                    <li key={index}>• {risk}</li>
                  ))}
                </ul>
              )}
            </div>
          )}
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Quick Actions */}
          <div className="bg-white rounded-lg shadow-md p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">
              {t('ai.quickActions')}
            </h3>
            <div className="space-y-3">
              <Link
                to={`/market/product/${recommendation.product_type || recommendation.product}`}
                className="w-full inline-flex items-center justify-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700"
              >
                <ChartBarIcon className="h-4 w-4 mr-2" />
                {t('ai.viewMarketAnalysis')}
              </Link>

              <Link
                to="/dashboard/products"
                className="w-full inline-flex items-center justify-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
              >
                <ShoppingCartIcon className="h-4 w-4 mr-2" />
                {t('ai.viewProducts')}
              </Link>
            </div>
          </div>

          {/* Related Recommendations */}
          {relatedRecommendations.length > 0 && (
            <div className="bg-white rounded-lg shadow-md p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">
                {t('ai.relatedRecommendations')}
              </h3>
              <div className="space-y-3">
                {relatedRecommendations.map((related, index) => (
                  <Link
                    key={index}
                    to={`/ai/recommendations/${related.id}`}
                    className="block p-3 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
                  >
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-gray-900">
                          {related.product_type || related.product}
                        </p>
                        <p className="text-xs text-gray-500">
                          {related.action}
                        </p>
                      </div>
                      {getRecommendationIcon(related.action)}
                    </div>
                  </Link>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

export default RecommendationDetails
