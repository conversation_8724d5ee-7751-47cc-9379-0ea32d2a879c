import {
    IconDefinition,
    IconName,
    IconStyle,
    IconFamily
} from '@fortawesome/fontawesome-common-types';

export type IconMacroParams = {
    name: IconName,
    style?: IconStyle,
    family?: IconFamily
};

export function brands(iconName: IconName): IconDefinition;
export function duotone(iconName: IconName): IconDefinition;
export function light(iconName: IconName): IconDefinition;
export function regular(iconName: IconName): IconDefinition;
export function solid(iconName: IconName): IconDefinition;
export function thin(iconName: IconName): IconDefinition;
export function icon(params: IconMacroParams): IconDefinition;