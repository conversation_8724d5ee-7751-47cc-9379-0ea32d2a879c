# @fortawesome/fontawesome-common-types - SVG with JavaScript

> "I came here to chew bubblegum and install Font Awesome 6 - and I'm all out of bubblegum"

[![npm](https://img.shields.io/npm/v/@fortawesome/fontawesome-common-types.svg?style=flat-square)](https://www.npmjs.com/package/@fortawesome/fontawesome-common-types)

## What is this package?

Font Awesome 6 JavaScript packages support TypeScript. This package abstracts out some of the common definitions that those packages use.

## Here be dragons

If you are trying to import types from this package we *highly* recommend you do the following instead as *all types in this package are re-exported to the main fontawesome package*.

your.ts

```
import {
  IconName
} from `@fortawesome/fontawesome-svg-core`

const myIcon: IconName = "..."
```

## Issues and support

Start with [GitHub issues](https://github.com/FortAwesome/Font-Awesome/issues) and ping us on [Twitter](https://twitter.com/fontawesome) if you need to.
