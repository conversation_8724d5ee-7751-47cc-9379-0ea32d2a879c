# @fortawesome/fontawesome-svg-core - SVG with JavaScript version

> "I came here to chew bubblegum and install Font Awesome 6 - and I'm all out of bubblegum"

[![npm](https://img.shields.io/npm/v/@fortawesome/fontawesome-svg-core.svg?style=flat-square)](https://www.npmjs.com/package/@fortawesome/fontawesome-svg-core)

## Installation

```
$ npm i --save @fortawesome/fontawesome-svg-core
```

Or

```
$ yarn add @fortawesome/fontawesome-svg-core
```

## Documentation

Get started [here](https://docs.fontawesome.com/web/setup/get-started). Continue your journey [here](https://docs.fontawesome.com/web/setup/packages).

Or go straight to the [API documentation](https://docs.fontawesome.com/apis/javascript/get-started).

## Issues and support

Start with [GitHub issues](https://github.com/FortAwesome/Font-Awesome/issues) and ping us on [Twitter](https://twitter.com/fontawesome) if you need to.
